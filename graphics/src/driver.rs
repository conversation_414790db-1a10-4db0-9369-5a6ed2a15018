use crate::backend::driver_enums::*;
use crate::backend::vulkan::driver::VulkanDriver;
use std::any::Any;
use tracing::debug;

/// Driver是对底层图形API的抽象，提供统一的接口
/// 不同的图形API（如Vulkan、OpenGL、Metal等）都需要实现这个特性
pub trait Driver: Any + Send + Sync {
    /// 获取驱动类型
    fn get_backend_type(&self) -> Backend;

    /// 获取着色器模型
    fn get_shader_model(&self) -> ShaderModel;

    /// 获取着色器语言
    fn get_shader_language(&self) -> ShaderLanguage;

    /// 初始化驱动
    fn init(&mut self) -> Result<(), String>;

    /// 清理资源
    fn cleanup(&mut self);

    /// 等待GPU空闲
    fn wait_idle(&self);

    /// 获取Vulkan驱动（如果是Vulkan后端）
    fn as_vulkan_driver(&self) -> Option<&VulkanDriver> {
        None
    }

    /// 获取Vulkan驱动可变引用（如果是Vulkan后端）
    fn as_vulkan_driver_mut(&mut self) -> Option<&mut VulkanDriver> {
        None
    }

    /// 创建交换链
    fn create_swap_chain(&mut self, window: &dyn Any, flags: u64) -> Result<Box<dyn Any>, String>;

    /// 销毁交换链
    fn destroy_swap_chain(&mut self, swap_chain: Box<dyn Any>);

    /// 创建顶点缓冲区
    fn create_vertex_buffer(&mut self, size: usize, usage: u32) -> Result<u64, String>;

    /// 创建索引缓冲区
    fn create_index_buffer(&mut self, size: usize, usage: u32) -> Result<u64, String>;

    /// 创建纹理
    fn create_texture(&mut self, width: u32, height: u32, format: u32, usage: u32) -> Result<u64, String>;

    /// 创建着色器程序
    fn create_program(&mut self, vertex_shader: &[u8], fragment_shader: &[u8]) -> Result<u64, String>;

    /// 绘制命令
    fn draw(&mut self, primitive_type: PrimitiveType, vertex_count: u32, instance_count: u32) {
        // 默认实现，子类可以覆盖
        let _ = (primitive_type, vertex_count, instance_count);
    }

    /// 计算着色器调度
    fn dispatch_compute(&mut self, x: u32, y: u32, z: u32);

    /// 开始渲染通道
    fn begin_render_pass(&mut self, render_target: u64, clear_color: [f32; 4], clear_depth: f32, clear_stencil: u32) {
        // 默认实现，子类可以覆盖
        let _ = (render_target, clear_color, clear_depth, clear_stencil);
    }

    /// 结束渲染通道
    fn end_render_pass(&mut self) {
        // 默认实现，子类可以覆盖
    }

    /// 设置视口
    fn set_viewport(&mut self, x: i32, y: i32, width: u32, height: u32) {
        // 默认实现，子类可以覆盖
        let _ = (x, y, width, height);
    }

    /// 设置裁剪矩形
    fn set_scissor(&mut self, x: i32, y: i32, width: u32, height: u32) {
        // 默认实现，子类可以覆盖
        let _ = (x, y, width, height);
    }

    /// 绑定顶点缓冲区
    fn bind_vertex_buffer(&mut self, buffer: u64, offset: u64) {
        // 默认实现，子类可以覆盖
        let _ = (buffer, offset);
    }

    /// 绑定索引缓冲区
    fn bind_index_buffer(&mut self, buffer: u64, offset: u64, index_type: u32) {
        // 默认实现，子类可以覆盖
        let _ = (buffer, offset, index_type);
    }

    /// 绑定纹理
    fn bind_texture(&mut self, texture: u64, sampler: u64, binding: u32);

    /// 绑定着色器程序
    fn bind_program(&mut self, program: u64);

    /// 更新顶点缓冲区数据
    fn update_vertex_buffer(&mut self, buffer: u64, offset: u64, data: &[u8]);

    /// 更新索引缓冲区数据
    fn update_index_buffer(&mut self, buffer: u64, offset: u64, data: &[u8]);

    /// 更新统一缓冲区数据
    fn update_uniform_buffer(&mut self, buffer: u64, offset: u64, data: &[u8]) {
        // 默认实现，可以被具体的驱动覆盖
        debug!("update_uniform_buffer not implemented for this driver");
    }

    /// 更新纹理数据
    fn update_texture(&mut self, texture: u64, level: u32, x: u32, y: u32, width: u32, height: u32, data: &[u8]);

    /// 创建栅栏
    fn create_fence(&mut self) -> Result<u64, String>;

    /// 等待栅栏
    fn wait_fence(&self, fence: u64, timeout: u64) -> FenceStatus;

    /// 销毁栅栏
    fn destroy_fence(&mut self, fence: u64);

    /// 创建查询
    fn create_query(&mut self, query_type: u32) -> Result<u64, String>;

    /// 开始查询
    fn begin_query(&mut self, query: u64);

    /// 结束查询
    fn end_query(&mut self, query: u64);

    /// 获取查询结果
    fn get_query_result(&self, query: u64) -> TimerQueryResult;

    /// 销毁查询
    fn destroy_query(&mut self, query: u64);

    /// 添加调试标签
    fn push_debug_group(&mut self, name: &str);

    /// 移除调试标签
    fn pop_debug_group(&mut self);

    /// 插入调试标记
    fn insert_debug_marker(&mut self, name: &str);
}

/// 驱动工厂，用于创建不同类型的驱动实例
pub struct DriverFactory;

impl DriverFactory {
    /// 创建指定类型的驱动实例
    pub fn create(backend: Backend) -> Result<Box<dyn Driver>, String> {
        // 解析后端类型（如果是Default，则选择平台默认值）
        let resolved_backend = backend.resolve();

        match resolved_backend {
            Backend::Vulkan => {
                // 创建VulkanDriver
                return Ok(Box::new(crate::backend::vulkan::driver::VulkanDriver::new_without_window()));
            },
            Backend::OpenGL => {
                #[cfg(feature = "opengl")]
                {
                    // 这里需要导入并返回OpenGLDriver的实例
                    // return Ok(Box::new(crate::backend::opengl::OpenGLDriver::new()));
                    return Err("OpenGL backend is not implemented yet".to_string());
                }

                #[cfg(not(feature = "opengl"))]
                {
                    return Err("OpenGL backend is not enabled in this build".to_string());
                }
            },
            
            Backend::WebGPU => {
                #[cfg(feature = "webgpu")]
                {
                    // 这里需要导入并返回WebGPUDriver的实例
                    // return Ok(Box::new(crate::backend::webgpu::WebGPUDriver::new()));
                    return Err("WebGPU backend is not implemented yet".to_string());
                }

                #[cfg(not(feature = "webgpu"))]
                {
                    return Err("WebGPU backend is not enabled in this build".to_string());
                }
            },
            Backend::NoOp => {
                // 返回一个空实现，用于测试
                return Err("NoOp backend is not implemented yet".to_string());
            },
            _ => {
                return Err(format!("Unsupported backend: {}", backend));
            }
        }
    }
}
