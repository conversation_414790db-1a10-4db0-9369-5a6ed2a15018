// 从Filament的DriverEnums.h迁移而来
// 包含图形后端相关的枚举类型

use std::fmt;

/// 选择特定Engine应该使用的驱动程序
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
#[repr(u8)]
pub enum Backend {
    /// 自动为平台选择合适的驱动程序
    Default = 0,
    /// 选择OpenGL/ES驱动程序（Android上的默认选项）
    OpenGL = 1,
    /// 如果平台支持，选择Vulkan驱动程序（Linux/Windows上的默认选项）
    Vulkan = 2,
   
    /// 如果平台支持WebGPU，选择WebGPU驱动程序
    WebGPU = 3,
    /// 选择无操作驱动程序用于测试目的
    NoOp = 4,
}

impl Backend {
    /// 将Backend枚举转换为字符串表示
    pub fn to_string(&self) -> &'static str {
        match self {
            Backend::NoOp => "Noop",
            Backend::OpenGL => "OpenGL",
            Backend::Vulkan => "Vulkan",
            Backend::WebGPU => "WebGPU",
            Backend::Default => "Default",
        }
    }

    /// 根据当前平台选择默认的后端
    pub fn get_default_backend() -> Self {
        // 根据平台选择合适的后端
        #[cfg(target_os = "windows")]
        return Backend::Vulkan;

        #[cfg(target_os = "linux")]
        return Backend::Vulkan;

        #[cfg(target_os = "macos")]
        return Backend::Metal;

        #[cfg(target_os = "ios")]
        return Backend::Metal;

        #[cfg(target_os = "android")]
        return Backend::OpenGL;

        #[cfg(target_arch = "wasm32")]
        return Backend::WebGPU;

        // 如果没有匹配的平台，返回Vulkan作为默认选项
        #[allow(unreachable_code)]
        Backend::Vulkan
    }

    /// 解析Backend枚举，如果是Default则返回平台默认值
    pub fn resolve(&self) -> Self {
        match self {
            Backend::Default => Self::get_default_backend(),
            _ => *self,
        }
    }
}

impl fmt::Display for Backend {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.to_string())
    }
}

/// 定义着色器语言。与上面的后端枚举类似，但有一些区别：
/// - OpenGL后端可以在两种着色器语言之间选择：ESSL 1.0和ESSL 3.0
/// - Metal后端可以优先使用预编译的Metal库，同时回退到MSL
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum ShaderLanguage {
    SPIRV = 0,
    SLANG = 1,
    ESSL3 = 2,
    WGSL = 3,
}

impl ShaderLanguage {
    /// 将ShaderLanguage枚举转换为字符串表示
    pub fn to_string(&self) -> &'static str {
        match self {
            ShaderLanguage::SPIRV => "SPIR-V",
            ShaderLanguage::SLANG => "Slang",
            ShaderLanguage::ESSL3 => "ESSL 3.0",
            ShaderLanguage::WGSL => "WGSL",
        }
    }
}

impl fmt::Display for ShaderLanguage {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.to_string())
    }
}

/// 着色器阶段
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
#[repr(u8)]
pub enum ShaderStage {
    Vertex = 0,
    Fragment = 1,
    Compute = 2,
}

/// 着色器阶段标志
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct ShaderStageFlags(pub u8);

impl ShaderStageFlags {
    pub const NONE: ShaderStageFlags = ShaderStageFlags(0);
    pub const VERTEX: ShaderStageFlags = ShaderStageFlags(0x1);
    pub const FRAGMENT: ShaderStageFlags = ShaderStageFlags(0x2);
    pub const COMPUTE: ShaderStageFlags = ShaderStageFlags(0x4);
    pub const ALL: ShaderStageFlags = ShaderStageFlags(0x7); // VERTEX | FRAGMENT | COMPUTE
}

/// Fence::wait()的错误代码
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
#[repr(i8)]
pub enum FenceStatus {
    /// 发生错误。Fence条件未满足。
    Error = -1,
    /// Fence条件已满足。
    ConditionSatisfied = 0,
    /// wait()的超时已过期。Fence条件未满足。
    TimeoutExpired = 1,
}

/// 着色器模型
///
/// 这些枚举值在所有后端中使用，并指代功能和质量级别。
///
/// 例如，如果OpenGL后端支持OpenGL ES，则返回`Mobile`；如果支持桌面OpenGL，则返回`Desktop`，
/// 这稍后用于选择适当的着色器。
///
/// 着色器质量与性能也受ShaderModel影响。
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
#[repr(u8)]
pub enum ShaderModel {
    /// 移动级别功能
    Mobile = 1,
    /// 桌面级别功能
    Desktop = 2,
}

impl ShaderModel {
    /// 将ShaderModel枚举转换为字符串表示
    pub fn to_string(&self) -> &'static str {
        match self {
            ShaderModel::Mobile => "Mobile",
            ShaderModel::Desktop => "Desktop",
        }
    }
}

impl fmt::Display for ShaderModel {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.to_string())
    }
}

/// 图元类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
#[repr(u8)]
pub enum PrimitiveType {
    /// 点
    Points = 0,
    /// 线
    Lines = 1,
    /// 线带
    LineStrip = 3,
    /// 三角形
    Triangles = 4,
    /// 三角形带
    TriangleStrip = 5,
}

/// 计时器查询结果
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
#[repr(i8)]
pub enum TimerQueryResult {
    /// 发生错误，结果将不可用
    Error = -1,
    /// 结果尚未准备好
    NotReady = 0,
    /// 结果可用
    Available = 1,
}

/// 请求带有alpha通道的SwapChain
pub const SWAP_CHAIN_CONFIG_TRANSPARENT: u64 = 0x1;

/// 此标志表示交换链可用作读取渲染结果的源表面。
/// 创建将用作blit操作源的任何SwapChain时，必须设置此配置标志。
pub const SWAP_CHAIN_CONFIG_READABLE: u64 = 0x2;

/// 用于Fence::wait()的永久等待
pub const FENCE_WAIT_FOR_EVER: u64 = u64::MAX;
