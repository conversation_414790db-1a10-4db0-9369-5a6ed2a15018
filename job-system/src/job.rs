use std::sync::atomic::{AtomicU8, AtomicU32, Ordering};
use crate::job_system::{ThreadId, INVALID_THREAD_ID, WAITER_COUNT_SHIFT};

/// Job function type, equivalent to C++ JobFunc
pub type JobFunc = fn(*mut u8, &crate::job_system::job_system::JobSystem, &Job);

/// A job represents a unit of work that can be executed by the job system.
/// This is equivalent to the C++ Job class in Filament's JobSystem.
#[repr(C, align(64))] // Ensure cache line alignment
pub struct Job {
    /// Storage for job data, typically a std::function or similar
    /// Size is chosen to store at least std::function<>
    pub storage: [*mut u8; 6], // 48 bytes on 64-bit systems
    
    /// Function to execute when the job runs
    pub function: Option<JobFunc>,
    
    /// Parent job index (0x7FFF = no parent)
    pub parent: u16,
    
    /// Thread ID that is currently executing this job
    pub id: AtomicU8,
    
    /// Reference count for the job
    pub ref_count: AtomicU8,
    
    /// Running job count (lower 24 bits) + waiter count (upper 8 bits)
    pub running_job_count: AtomicU32,
}

impl Job {
    /// Create a new job with default values
    pub fn new() -> Self {
        Self {
            storage: [std::ptr::null_mut(); 6],
            function: None,
            parent: 0x7FFF, // No parent
            id: AtomicU8::new(INVALID_THREAD_ID),
            ref_count: AtomicU8::new(1),
            running_job_count: AtomicU32::new(1),
        }
    }

    /// Check if the job has completed (running job count is 0)
    pub fn has_completed(&self) -> bool {
        (self.running_job_count.load(Ordering::Acquire) & crate::job_system::JOB_COUNT_MASK) == 0
    }

    /// Get the current running job count
    pub fn get_running_job_count(&self) -> u32 {
        self.running_job_count.load(Ordering::Acquire) & crate::job_system::JOB_COUNT_MASK
    }

    /// Get the current waiter count
    pub fn get_waiter_count(&self) -> u32 {
        self.running_job_count.load(Ordering::Acquire) >> crate::job_system::WAITER_COUNT_SHIFT
    }

    /// Increment the reference count
    pub fn inc_ref(&self) {
        self.ref_count.fetch_add(1, Ordering::Relaxed);
    }

    /// Decrement the reference count and return the new count
    pub fn dec_ref(&self) -> u8 {
        self.ref_count.fetch_sub(1, Ordering::AcqRel) - 1
    }

    /// Get the current reference count
    pub fn get_ref_count(&self) -> u8 {
        self.ref_count.load(Ordering::Relaxed)
    }

    /// Set the thread ID
    pub fn set_thread_id(&self, id: ThreadId) {
        self.id.store(id, Ordering::Relaxed);
    }

    /// Get the thread ID
    pub fn get_thread_id(&self) -> ThreadId {
        self.id.load(Ordering::Relaxed)
    }

    /// Check if this job has a parent
    pub fn has_parent(&self) -> bool {
        self.parent != 0x7FFF
    }

    /// Get the parent job index
    pub fn get_parent_index(&self) -> u16 {
        self.parent
    }

    /// Set the parent job index
    pub fn set_parent_index(&mut self, parent: u16) {
        self.parent = parent;
    }

    /// Set the job function
    pub fn set_function(&mut self, func: JobFunc) {
        self.function = Some(func);
    }

    /// Get the job function
    pub fn get_function(&self) -> Option<JobFunc> {
        self.function
    }

    /// Execute the job function
    pub fn execute(&self, job_system: &crate::job_system::job_system::JobSystem) {
        if let Some(func) = self.function {
            func(self.storage.as_ptr() as *mut u8, job_system, self);
        }
    }
}

impl Default for Job {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::job_system::{ThreadId, INVALID_THREAD_ID, WAITER_COUNT_SHIFT};

    #[test]
    fn test_job_new() {
        let job = Job::new();
        assert_eq!(job.get_ref_count(), 1);
        assert_eq!(job.get_thread_id(), INVALID_THREAD_ID);
        assert_eq!(job.get_running_job_count(), 1);
        assert_eq!(job.get_waiter_count(), 0);
        assert!(!job.has_parent());
        assert_eq!(job.get_parent_index(), 0x7FFF);
        assert!(job.get_function().is_none());
    }

    #[test]
    fn test_job_reference_counting() {
        let job = Job::new();
        assert_eq!(job.get_ref_count(), 1);
        
        job.inc_ref();
        assert_eq!(job.get_ref_count(), 2);
        
        let count = job.dec_ref();
        assert_eq!(count, 1);
        assert_eq!(job.get_ref_count(), 1);
    }

    #[test]
    fn test_job_thread_id() {
        let job = Job::new();
        assert_eq!(job.get_thread_id(), INVALID_THREAD_ID);
        
        job.set_thread_id(5);
        assert_eq!(job.get_thread_id(), 5);
    }

    #[test]
    fn test_job_parent() {
        let mut job = Job::new();
        assert!(!job.has_parent());
        
        job.set_parent_index(123);
        assert!(job.has_parent());
        assert_eq!(job.get_parent_index(), 123);
    }

    #[test]
    fn test_job_function() {
        let mut job = Job::new();
        assert!(job.get_function().is_none());
        
        let test_func: JobFunc = |_storage, _js, _job| {
            // Test function that does nothing
        };
        
        job.set_function(test_func);
        assert!(job.get_function().is_some());
    }

    #[test]
    fn test_job_completion() {
        let job = Job::new();
        assert!(!job.has_completed());
        assert_eq!(job.get_running_job_count(), 1);
        
        // Simulate job completion by decrementing running count
        job.running_job_count.fetch_sub(1, Ordering::AcqRel);
        assert!(job.has_completed());
        assert_eq!(job.get_running_job_count(), 0);
    }

    #[test]
    fn test_job_waiter_count() {
        let job = Job::new();
        assert_eq!(job.get_waiter_count(), 0);
        
        // Simulate adding a waiter
        job.running_job_count.fetch_add(1 << WAITER_COUNT_SHIFT, Ordering::Relaxed);
        assert_eq!(job.get_waiter_count(), 1);
    }

    #[test]
    fn test_job_default() {
        let job = Job::default();
        assert_eq!(job.get_ref_count(), 1);
        assert_eq!(job.get_thread_id(), INVALID_THREAD_ID);
    }
} 