/// A generic, lockless, fixed-size work-stealing dequeue.
///
/// This implementation is based on the Chase-Lev Deque algorithm. The basic
/// design is a circular array with two indices, `top` and `bottom`. The "main"
/// thread, which owns the deque, can `push` and `pop` from the `bottom` of
/// the queue. Any other "thief" thread can `steal` from the `top`.
///
/// This creates a separation of concerns: the owner has fast, almost uncontended
/// access to its end of the deque, while multiple thieves can concurrently try
/// to take work from the other end.
///
/// ```text
///      top (steal)                        bottom (push, pop)
///        v                                     v
///        |----|----|----|----|----|----|----|----|
///      Thief threads                     Owner thread
/// ```
use std::mem::MaybeUninit;
use std::sync::atomic::{AtomicI64, Ordering};


pub struct WorkStealingDequeue<ItemType, const N: usize> {
    top: AtomicI64,
    bottom: AtomicI64,
    items: [MaybeUninit<ItemType>; N],
}

impl<ItemType, const N: usize> WorkStealingDequeue<ItemType, N> {
    /// A bitmask used to wrap indices around the circular array.
    /// This only works correctly if N is a power of two.
    const MASK: i64 = (N - 1) as i64;

    /// Creates a new, empty work-stealing dequeue.
    ///
    /// # Panics
    ///
    /// Panics if `N` is not a power of two.
    pub fn new() -> Self {
        assert!(
            N.is_power_of_two(),
            "N must be a power of two for the bitmask to work correctly."
        );
        WorkStealingDequeue {
            top: AtomicI64::new(0),
            bottom: AtomicI64::new(0),
            // This is the correct way to initialize an array of `MaybeUninit`
            // without requiring `ItemType` to be `Copy`.
            // SAFETY: We are creating an array of uninitialized data, which is safe.
            // The logic of the deque ensures that we never read an item before
            // it has been written.
            items: { unsafe { MaybeUninit::<[MaybeUninit<ItemType>; N]>::uninit().assume_init() } },
        }
    }

    /// Adds an item to the bottom of the queue.
    ///
    /// This operation should only be called by the owner thread.
    ///
    /// # Returns
    ///
    /// - `Ok(())` if the push was successful.
    /// - `Err(item)` if the queue is full, returning the item that could not be pushed.
    pub fn push(&mut self, item: ItemType) -> Result<(), ItemType> {
        // Since this method takes `&mut self`, we have exclusive access from the
        // owner's perspective. We can load `bottom` and `top` with relaxed ordering.
        let bottom = self.bottom.load(Ordering::Relaxed);
        let top = self.top.load(Ordering::Relaxed);

        // --- CORRECTNESS FIX: Check if the queue is full ---
        if bottom.wrapping_sub(top) >= N as i64 {
            return Err(item);
        }

        let index = (bottom & Self::MASK) as usize;

        // SAFETY: The check above ensures we have space. The `&mut self` guarantees
        // no other thread is pushing. We can safely write to this slot.
        unsafe {
            // Using `get_unchecked_mut` is slightly more performant as it skips
            // bounds checks, which we've already logically ensured with the bitmask.
            self.items.get_unchecked_mut(index).write(item);
        }

        // --- PERFORMANCE OPTIMIZATION: Use Release ordering ---
        // We use `Release` ordering to publish the write. This ensures that the
        // write to the `items` array happens-before this store. Any thread that
        // sees the new `bottom` value with `Acquire` ordering will also see the
        // item that was just written.
        self.bottom.store(bottom + 1, Ordering::Release);

        Ok(())
    }

    pub fn pop(&mut self) -> Option<ItemType> {
        let bottom = *self.bottom.get_mut();

        // Fast-path check for an empty queue.
        let top = self.top.load(Ordering::Acquire);
        if top >= bottom {
            return None;
        }

        // Tentatively claim the item.
        let new_bottom = bottom - 1;
        *self.bottom.get_mut() = new_bottom;

        // Ensure the `bottom` write is visible before we re-read `top`.
        std::sync::atomic::fence(Ordering::SeqCst);
        let top = self.top.load(Ordering::Acquire);

        if top < new_bottom {
            // Fast path: More than one item was in the queue.
            // We have successfully popped an item without contention.
            let index = (new_bottom & Self::MASK) as usize;
            let item = unsafe { self.items.get_unchecked(index).as_ptr().read() };
            return Some(item);
        }

        // If we reach here, it means `top >= new_bottom`.
        // This implies the queue is either empty, or we are racing for the last item.

        // Since we did not succeed on the fast path, we must restore `bottom`.
        // We failed to pop, so the size of the queue should not change.
        *self.bottom.get_mut() = bottom;

        // Now, race with stealers for the last item (if it exists).
        // The condition `top == new_bottom` indicates there was one item.
        if top == new_bottom {
            // Attempt to claim the last item by incrementing `top`.
            let result = self.top.compare_exchange(
                top,
                top + 1,
                Ordering::AcqRel,
                Ordering::Relaxed,
            );

            if result.is_ok() {
                // We won the race.
                let index = (new_bottom & Self::MASK) as usize;
                let item = unsafe { self.items.get_unchecked(index).as_ptr().read() };
                return Some(item);
            }
        }

        // Either the queue was empty to begin with (`top > new_bottom`),
        // or we lost the race to a stealer. In both cases, the pop fails.
        None
    }

    /// Steals an item from the top of the queue.
    ///
    /// This can be called concurrently by any number of "thief" threads.
    pub fn steal(&self) -> Option<ItemType> {
        // A loop is used to retry in case of a race condition with other stealers.
        loop {
            // It is crucial to read `top` *before* `bottom` to get a consistent
            // snapshot of the queue's state.
            let top = self.top.load(Ordering::Acquire);
            let bottom = self.bottom.load(Ordering::Acquire);

            if top >= bottom {
                // The snapshot shows an empty queue.
                return None;
            }

            // The queue appears to have items. Let's try to steal one.
            let index = (top & Self::MASK) as usize;
            // SAFETY: We get a pointer to the data, but we do not read it yet.
            // Reading is only safe after we have won the CAS race.
            let item_ptr = unsafe { self.items.get_unchecked(index).as_ptr() };

            // Try to claim the item by atomically incrementing `top`.
            // `AcqRel` is used:
            // - `Acquire`: Synchronizes with other CAS operations on `top`.
            // - `Release`: Makes our update to `top` visible to the owner and other stealers.
            let result =
                self.top
                    .compare_exchange(top, top + 1, Ordering::AcqRel, Ordering::Acquire);

            if result.is_ok() {
                // CAS succeeded: we are the only thread that can access this item.
                // SAFETY: It is now safe to read the item.
                let item = unsafe { item_ptr.read() };
                return Some(item);
            }

            // CAS failed: another thread (owner or another stealer) modified `top`
            // before we could. We loop to retry the entire operation.
        }
    }

    /// Returns the total capacity of the queue.
    pub fn get_size(&self) -> usize {
        N
    }

    /// Returns an approximate number of items in the queue.
    ///
    /// This value can be stale immediately after it is returned due to
    /// concurrent operations.
    pub fn get_count(&self) -> usize {
        let bottom = self.bottom.load(Ordering::Relaxed);
        let top = self.top.load(Ordering::Relaxed);
        bottom.wrapping_sub(top) as usize
    }
}

/// A custom `Drop` implementation to ensure all remaining items in the
/// queue are properly dropped.
impl<ItemType, const N: usize> Drop for WorkStealingDequeue<ItemType, N> {
    fn drop(&mut self) {
        // Only run the drop logic if the item type actually needs dropping.
        if std::mem::needs_drop::<ItemType>() {
            // Since we have `&mut self`, no other threads can be accessing the deque.
            // We can safely treat the atomic pointers as regular variables.
            let bottom = *self.bottom.get_mut();
            let mut top = *self.top.get_mut();

            while top < bottom {
                let index = (top & Self::MASK) as usize;
                // SAFETY: We are iterating through the valid, initialized items
                // and dropping them in place.
                unsafe {
                    self.items
                        .get_unchecked_mut(index)
                        .as_mut_ptr()
                        .drop_in_place();
                }
                top += 1;
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::atomic::AtomicUsize;
    use std::sync::Arc;
    use std::thread;

    #[test]
    fn test_basic_push_pop() {
        let mut q = WorkStealingDequeue::<i32, 8>::new();
        assert_eq!(q.get_count(), 0);

        q.push(10).unwrap();
        q.push(20).unwrap();
        q.push(30).unwrap();
        assert_eq!(q.get_count(), 3);

        assert_eq!(q.pop(), Some(30));
        assert_eq!(q.pop(), Some(20));
        assert_eq!(q.get_count(), 1);

        assert_eq!(q.pop(), Some(10));
        assert_eq!(q.get_count(), 0);
        assert_eq!(q.pop(), None);
    }

    #[test]
    fn test_queue_full() {
        let mut q = WorkStealingDequeue::<i32, 4>::new();
        assert!(q.push(1).is_ok());
        assert!(q.push(2).is_ok());
        assert!(q.push(3).is_ok());
        assert!(q.push(4).is_ok());
        assert_eq!(q.get_count(), 4);

        // Queue is now full
        match q.push(5) {
            Err(5) => { /* Correct */ }
            _ => panic!("Should have returned an error"),
        }
    }

    #[test]
    fn test_simple_steal() {
        let mut q = WorkStealingDequeue::<i32, 8>::new();
        q.push(100).unwrap();
        q.push(200).unwrap();

        let q = Arc::new(q);
        let qc = q.clone();

        let handle = thread::spawn(move || {
            assert_eq!(qc.steal(), Some(100));
            assert_eq!(qc.steal(), Some(200));
            assert_eq!(qc.steal(), None);
        });

        handle.join().unwrap();
        assert_eq!(q.get_count(), 0);
    }

    #[test]
    fn test_concurrent_steal() {
        const NUM_ITEMS: usize = 1024;
        const NUM_THIEVES: usize = 4;
        let mut q = WorkStealingDequeue::<usize, NUM_ITEMS>::new();
        for i in 0..NUM_ITEMS {
            q.push(i).unwrap();
        }

        let q = Arc::new(q);
        let mut handles = vec![];
        let stolen_count = Arc::new(AtomicUsize::new(0));

        for _ in 0..NUM_THIEVES {
            let q_clone = q.clone();
            let stolen_count_clone = stolen_count.clone();
            handles.push(thread::spawn(move || {
                let mut count = 0;
                while q_clone.steal().is_some() {
                    count += 1;
                }
                stolen_count_clone.fetch_add(count, Ordering::SeqCst);
            }))
        }

        for handle in handles {
            handle.join().unwrap();
        }

        assert_eq!(stolen_count.load(Ordering::SeqCst), NUM_ITEMS);
        assert_eq!(q.get_count(), 0);
    }

    #[test]
    fn test_pop_steal_race() {
        const NUM_ROUNDS: usize = 5000;
        let mut pop_successes = 0;
        let mut steal_successes = 0;

        for i in 0..NUM_ROUNDS {
            let mut q = WorkStealingDequeue::<usize, 8>::new();
            q.push(i).unwrap();

            let q_arc = Arc::new(q);
            let q_ptr = Arc::into_raw(q_arc) as *mut WorkStealingDequeue<usize, 8>;

            // *** 核心改动：将指针转换为 usize 整数 ***
            // usize 是可以安全地在线程间发送的。
            let ptr_addr = q_ptr as usize;

            let (pop_res, steal_res) = thread::scope(|s| {
                // 线程一：捕获的是 ptr_addr (一个 usize)
                let pop_handle = s.spawn(move || {
                    // 在线程内部，将整数转换回指针
                    let q_ptr = ptr_addr as *mut WorkStealingDequeue<usize, 8>;
                    unsafe { (*q_ptr).pop() }
                });

                // 线程二：同样捕获 ptr_addr
                let steal_handle = s.spawn(move || {
                    // 在线程内部，也将整数转换回指针
                    let q_ptr = ptr_addr as *mut WorkStealingDequeue<usize, 8>;
                    unsafe { (*q_ptr).steal() }
                });

                (pop_handle.join().unwrap(), steal_handle.join().unwrap())
            });

            // 从裸指针重建 Arc 以便安全地释放内存
            unsafe {
                let _ = Arc::from_raw(q_ptr);
            }

            match (pop_res, steal_res) {
                (Some(val), None) => {
                    assert_eq!(val, i);
                    pop_successes += 1;
                }
                (None, Some(val)) => {
                    assert_eq!(val, i);
                    steal_successes += 1;
                }
                (Some(_), Some(_)) => {
                    panic!("Race failed: both pop and steal got the item!");
                }
                (None, None) => {
                    panic!("Race failed: item was lost!");
                }
            }
        }

        println!("Pop wins: {}, Steal wins: {}", pop_successes, steal_successes);
        assert_eq!(pop_successes + steal_successes, NUM_ROUNDS, "Total successes must equal the number of rounds");
        assert!(pop_successes > 0, "Pop never won the race.");
        assert!(steal_successes > 0, "Steal never won the race.");
    }

    #[test]
    fn test_drop() {
        // A static atomic is used to count drops across scopes.
        // It's reset to 0 at the start of the test to ensure test independence.
        static DROP_COUNT: AtomicUsize = AtomicUsize::new(0);
        DROP_COUNT.store(0, Ordering::SeqCst);

        // A simple struct that increments the global counter when dropped.
        #[derive(Debug, PartialEq)]
        struct DropChecker;

        impl Drop for DropChecker {
            fn drop(&mut self) {
                DROP_COUNT.fetch_add(1, Ordering::SeqCst);
            }
        }

        {
            let mut q = WorkStealingDequeue::<DropChecker, 16>::new();
            q.push(DropChecker).unwrap();
            q.push(DropChecker).unwrap();
            q.push(DropChecker).unwrap();

            // Pop one item and immediately drop it. The `pop` returns an `Option`,
            // and `drop` takes ownership and drops it, triggering the inner `DropChecker`'s
            // drop implementation.
            drop(q.pop());

            // We expect exactly one item to have been dropped at this point.
            assert_eq!(DROP_COUNT.load(Ordering::SeqCst), 1);

        } // `q` goes out of scope here. The queue's own `Drop` implementation is called.

        // The `Drop` impl for the queue should drop the remaining 2 items.
        // The total drop count should now be 1 (from the explicit drop) + 2 (from q.drop) = 3.
        assert_eq!(DROP_COUNT.load(Ordering::SeqCst), 3);
    }
}
