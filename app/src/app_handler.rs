//! Application handler for the Moonfield engine.

use moonfield_core::Engine;
use winit::application::ApplicationHandler;
use winit::event::WindowEvent;
use winit::event_loop::ActiveEventLoop;
use winit::window::{Window, WindowId};

/// Basic application handler that integrates Moonfield engine with winit
pub struct MoonfieldApp {
    engine: Option<Engine>,
    window: Option<Window>,
}

impl Default for MoonfieldApp {
    fn default() -> Self {
        Self {
            engine: None,
            window: None,
        }
    }
}

impl ApplicationHandler for MoonfieldApp {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if self.window.is_none() {
            self.window = Some(event_loop.create_window(Window::default_attributes()).unwrap());
        }
    }

    fn window_event(&mut self, event_loop: &ActiveEventLoop, _id: WindowId, event: WindowEvent) {
        match event {
            WindowEvent::CloseRequested => {
                event_loop.exit();
            }
            WindowEvent::RedrawRequested => {
                if let Some(window) = &self.window {
                    window.pre_present_notify();
                }
            }
            _ => {}
        }
    }
}