//! Tracking policies for memory allocators.
//!
//! This module provides different tracking policies that can be used with allocators:
//! - `Untracked`: No tracking (default)
//! - `HighWatermark`: Tracks the maximum memory usage
//! - `Debug`: Fills memory with patterns to help catch bugs
//! - `DebugAndHighWatermark`: Combines Debug and HighWatermark

use tracing::debug;

/// A trait for tracking memory allocations.
pub trait Tracker {
    /// Called when memory is allocated.
    fn on_alloc(&mut self, ptr: *mut u8, size: usize, alignment: usize, offset: usize);

    /// Called when memory is freed.
    fn on_free(&mut self, ptr: *mut u8, size: usize);

    /// Called when the allocator is reset.
    fn on_reset(&mut self);

    /// Called when the allocator is rewound.
    fn on_rewind(&mut self, addr: *mut u8);
}

/// A tracker that doesn't track anything (no-op).
#[derive(Default)]
pub struct Untracked;

impl Tracker for Untracked {
    fn on_alloc(&mut self, _ptr: *mut u8, _size: usize, _alignment: usize, _offset: usize) {}
    fn on_free(&mut self, _ptr: *mut u8, _size: usize) {}
    fn on_reset(&mut self) {}
    fn on_rewind(&mut self, _addr: *mut u8) {}
}

/// A tracker that tracks the maximum memory usage (high watermark).
pub struct HighWatermark {
    name: Option<String>,
    base: *mut u8,
    size: u32,
    current: u32,
    high_watermark: u32,
}

impl HighWatermark {
    /// Create a new high watermark tracker.
    pub fn new(name: Option<&str>, base: *mut u8, size: usize) -> Self {
        Self {
            name: name.map(String::from),
            base,
            size: size as u32,
            current: 0,
            high_watermark: 0,
        }
    }

    /// Get the current high watermark.
    pub fn get_high_watermark(&self) -> u32 {
        self.high_watermark
    }
}

impl Drop for HighWatermark {
    fn drop(&mut self) {
        let wm = self.high_watermark;
        if self.size > 0 {
            let wmpct = (wm as f64 / self.size as f64 * 100.0) as u32;
            if wmpct > 80 {
                if let Some(name) = &self.name {
                    debug!(
                        "{} arena: High watermark {} KiB ({}%)",
                        name,
                        wm / 1024,
                        wmpct
                    );
                } else {
                    debug!("Arena: High watermark {} KiB ({}%)", wm / 1024, wmpct);
                }
            }
        } else if let Some(name) = &self.name {
            debug!("{} arena: High watermark {} KiB", name, wm / 1024);
        } else {
            debug!("Arena: High watermark {} KiB", wm / 1024);
        }
    }
}

impl Tracker for HighWatermark {
    fn on_alloc(&mut self, _ptr: *mut u8, size: usize, _alignment: usize, _offset: usize) {
        self.current += size as u32;
        self.high_watermark = self.current.max(self.high_watermark);
    }

    fn on_free(&mut self, _ptr: *mut u8, size: usize) {
        debug_assert!(self.current >= size as u32);
        self.current = self.current.saturating_sub(size as u32);
    }

    fn on_reset(&mut self) {
        debug_assert!(!self.base.is_null());
        self.current = 0;
    }

    fn on_rewind(&mut self, addr: *mut u8) {
        debug_assert!(!self.base.is_null());
        // For LinearAllocatorWithFallback we could get pointers outside the range
        if addr >= self.base && unsafe { addr.offset_from(self.base) } < self.size as isize {
            self.current = unsafe { addr.offset_from(self.base) } as u32;
        }
    }
}

/// A tracker that fills memory with patterns to help catch bugs.
pub struct Debug {
    name: Option<String>,
    base: *mut u8,
    size: u32,
}

impl Debug {
    /// Create a new debug tracker.
    pub fn new(name: Option<&str>, base: *mut u8, size: usize) -> Self {
        Self {
            name: name.map(String::from),
            base,
            size: size as u32,
        }
    }
}

impl Tracker for Debug {
    fn on_alloc(&mut self, ptr: *mut u8, size: usize, _alignment: usize, _offset: usize) {
        if !ptr.is_null() {
            unsafe {
                std::ptr::write_bytes(ptr, 0xEB, size);
            }
        }
    }

    fn on_free(&mut self, ptr: *mut u8, size: usize) {
        if !ptr.is_null() {
            unsafe {
                std::ptr::write_bytes(ptr, 0xEF, size);
            }
        }
    }

    fn on_reset(&mut self) {
        debug_assert!(!self.base.is_null());
        unsafe {
            std::ptr::write_bytes(self.base, 0xEC, self.size as usize);
        }
    }

    fn on_rewind(&mut self, addr: *mut u8) {
        debug_assert!(!self.base.is_null());
        debug_assert!(addr >= self.base);
        unsafe {
            let size = self.base.offset(self.size as isize).offset_from(addr) as usize;
            std::ptr::write_bytes(addr, 0x55, size);
        }
    }
}

/// A tracker that combines Debug and HighWatermark.
pub struct DebugAndHighWatermark {
    high_watermark: HighWatermark,
    debug: Debug,
}

impl DebugAndHighWatermark {
    /// Create a new debug and high watermark tracker.
    pub fn new(name: Option<&str>, base: *mut u8, size: usize) -> Self {
        Self {
            high_watermark: HighWatermark::new(name, base, size),
            debug: Debug::new(name, base, size),
        }
    }

    /// Get the current high watermark.
    pub fn get_high_watermark(&self) -> u32 {
        self.high_watermark.get_high_watermark()
    }
}

impl Tracker for DebugAndHighWatermark {
    fn on_alloc(&mut self, ptr: *mut u8, size: usize, alignment: usize, offset: usize) {
        self.high_watermark.on_alloc(ptr, size, alignment, offset);
        self.debug.on_alloc(ptr, size, alignment, offset);
    }

    fn on_free(&mut self, ptr: *mut u8, size: usize) {
        self.high_watermark.on_free(ptr, size);
        self.debug.on_free(ptr, size);
    }

    fn on_reset(&mut self) {
        self.high_watermark.on_reset();
        self.debug.on_reset();
    }

    fn on_rewind(&mut self, addr: *mut u8) {
        self.high_watermark.on_rewind(addr);
        self.debug.on_rewind(addr);
    }
}
