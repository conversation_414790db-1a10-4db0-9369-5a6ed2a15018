[package]
name = "moonfield-core"
version = "0.1.0"
edition = "2021"
description = "Core engine functionality for the Moonfield engine"
license = "MIT OR Apache-2.0"

[dependencies]
moonfield-graphics = { path = "../graphics" }
moonfield-ecs = { path = "../ecs" }
moonfield-job-system = { path = "../job-system" }
moonfield-allocator = { path = "../allocator" }
tracing = { workspace = true }
nalgebra-glm = { workspace = true }
tobj = { workspace = true }
anyhow = { workspace = true }

[features]
default = []
