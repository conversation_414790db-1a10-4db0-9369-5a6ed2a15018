//! Core engine functionality for the Moonfield engine.
//!
//! This crate provides the main engine implementation, utilities,
//! and high-level APIs for the Moonfield game engine.

pub mod engine;
pub mod fs;
pub mod math;

// Re-export commonly used items
pub use engine::Engine;

// Re-export from dependencies for convenience
pub use moonfield_graphics as graphics;
pub use moonfield_ecs as ecs;
pub use moonfield_job_system as job_system;
pub use moonfield_allocator as allocator;
