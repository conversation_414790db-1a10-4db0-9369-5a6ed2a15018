use crate::utils::allocator::Allocator;
use std::any::TypeId;
use std::mem::{size_of, align_of};
use std::ptr::NonNull;
use std::marker::PhantomData;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, PartialEq, Eq)]
pub struct TypeInfo {
    id: TypeId,
    size: usize,
    align: usize,
}

impl TypeInfo {
    pub fn of<T: 'static>() -> Self {
        Self {
            id: TypeId::of::<T>(),
            size: size_of::<T>(),
            align: align_of::<T>(),
        }
    }

    pub fn id(&self) -> TypeId {
        self.id
    }
    pub fn size(&self) -> usize {
        self.size
    }
    pub fn align(&self) -> usize {
        self.align
    }
}

pub trait BundleDef: Sized + 'static {
    const COUNT: usize;

    fn with_type_infos<R>(f: impl FnOnce(&[TypeInfo]) -> R) -> R;

    /// Get the layout information for all components in this bundle
    fn get_layouts() -> Vec<(usize, usize)> {
        Self::with_type_infos(|infos| {
            infos.iter().map(|info| (info.size(), info.align())).collect()
        })
    }
}


#[derive(Debug)]
pub struct StructureOfArrays<B: BundleDef, A: Allocator> {
    /// Number of bundles currently stored
    num_of_bundles: usize,
    /// Maximum capacity before reallocation
    capacity: usize,
    /// Array of pointers to component arrays
    arrays: Vec<Option<NonNull<u8>>>,
    /// Allocator for memory management
    allocator: A,
    /// Phantom data to maintain type information
    _phantom: PhantomData<B>,
}

impl<B: BundleDef, A: Allocator> StructureOfArrays<B, A> {
    /// Create a new StructureOfArrays with the given allocator and initial capacity
    pub fn new(allocator: A, initial_capacity: usize) -> Self {
        let mut arrays = Vec::with_capacity(B::COUNT);

        // Initialize all arrays to None
        for _ in 0..B::COUNT {
            arrays.push(None);
        }

        let mut soa = Self {
            num_of_bundles: 0,
            capacity: 0,
            arrays,
            allocator,
            _phantom: PhantomData,
        };

        if initial_capacity > 0 {
            soa.reserve(initial_capacity);
        }

        soa
    }

    /// Get the number of bundles currently stored
    pub fn len(&self) -> usize {
        self.num_of_bundles
    }

    /// Check if the structure is empty
    pub fn is_empty(&self) -> bool {
        self.num_of_bundles == 0
    }

    /// Get the current capacity
    pub fn capacity(&self) -> usize {
        self.capacity
    }

    /// Reserve space for at least `additional` more bundles
    pub fn reserve(&mut self, new_capacity: usize) {
        if new_capacity <= self.capacity {
            return;
        }

        let layouts = B::get_layouts();
        let mut new_arrays = Vec::with_capacity(B::COUNT);

        for (i, (size, align)) in layouts.iter().enumerate() {
            let total_size = size * new_capacity;

            if let Some(ptr) = self.allocator.alloc_aligned(total_size, *align) {
                let new_ptr = NonNull::new(ptr).expect("Allocator returned null pointer");

                // Copy existing data if any
                if let Some(old_ptr) = self.arrays[i] {
                    if self.num_of_bundles > 0 {
                        unsafe {
                            std::ptr::copy_nonoverlapping(
                                old_ptr.as_ptr(),
                                new_ptr.as_ptr(),
                                size * self.num_of_bundles,
                            );
                        }
                    }

                    // Free old memory
                    unsafe {
                        self.allocator.free(old_ptr.as_ptr(), size * self.capacity);
                    }
                }

                new_arrays.push(Some(new_ptr));
            } else {
                panic!("Failed to allocate memory for component array");
            }
        }

        self.arrays = new_arrays;
        self.capacity = new_capacity;
    }

    /// Clear all bundles but keep allocated memory
    pub fn clear(&mut self) {
        self.num_of_bundles = 0;
    }

    /// Get a raw pointer to the component array at the given index
    ///
    /// # Safety
    ///
    /// The caller must ensure that:
    /// - `component_index` is less than `B::COUNT`
    /// - The returned pointer is used correctly with proper type casting
    /// - The pointer remains valid only while this StructureOfArrays exists
    pub unsafe fn get_component_array_ptr(&self, component_index: usize) -> Option<*mut u8> {
        if component_index >= B::COUNT {
            return None;
        }

        self.arrays[component_index].map(|ptr| ptr.as_ptr())
    }

    /// Get the size and alignment for a component at the given index
    pub fn get_component_layout(&self, component_index: usize) -> Option<(usize, usize)> {
        if component_index >= B::COUNT {
            return None;
        }

        let layouts = B::get_layouts();
        Some(layouts[component_index])
    }
}

impl<B: BundleDef, A: Allocator> Drop for StructureOfArrays<B, A> {
    fn drop(&mut self) {
        let layouts = B::get_layouts();

        // Free all allocated arrays
        for (i, array_ptr) in self.arrays.iter().enumerate() {
            if let Some(ptr) = array_ptr {
                let (size, _) = layouts[i];
                unsafe {
                    self.allocator.free(ptr.as_ptr(), size * self.capacity);
                }
            }
        }
    }
}

/// Macro to automatically implement BundleDef for a tuple of component types
///
/// # Example
///
/// ```rust
/// use moonfield::ecs::structure_of_arrays::*;
///
/// #[derive(Debug, Clone)]
/// struct Position { x: f32, y: f32, z: f32 }
///
/// #[derive(Debug, Clone)]
/// struct Velocity { x: f32, y: f32, z: f32 }
///
/// #[derive(Debug, Clone)]
/// struct Health { value: f32 }
///
/// // Define a bundle containing Position, Velocity, and Health components
/// impl_bundle_def!(MyBundle, Position, Velocity, Health);
/// ```
#[macro_export]
macro_rules! impl_bundle_def {
    ($bundle_name:ident, $($component:ty),+ $(,)?) => {
        pub struct $bundle_name;

        impl $crate::ecs::structure_of_arrays::BundleDef for $bundle_name {
            const COUNT: usize = count_types!($($component),+);

            fn with_type_infos<R>(f: impl FnOnce(&[$crate::ecs::structure_of_arrays::TypeInfo]) -> R) -> R {
                let type_infos = [
                    $($crate::ecs::structure_of_arrays::TypeInfo::of::<$component>()),+
                ];
                f(&type_infos)
            }
        }
    };
}

/// Helper macro to count the number of types
#[macro_export]
macro_rules! count_types {
    () => { 0 };
    ($head:ty) => { 1 };
    ($head:ty, $($tail:ty),+) => { 1 + count_types!($($tail),+) };
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::allocator::heap::HeapAllocator;
    use crate::impl_bundle_def;

    #[derive(Debug, Clone, PartialEq)]
    struct Position {
        x: f32,
        y: f32,
        z: f32,
    }

    #[derive(Debug, Clone, PartialEq)]
    struct Velocity {
        x: f32,
        y: f32,
        z: f32,
    }

    #[derive(Debug, Clone, PartialEq)]
    struct Health {
        value: f32,
    }

    // Define a bundle using our macro
    impl_bundle_def!(TestBundle, Position, Velocity, Health);

    #[test]
    fn test_type_info() {
        let pos_info = TypeInfo::of::<Position>();
        assert_eq!(pos_info.size(), std::mem::size_of::<Position>());
        assert_eq!(pos_info.align(), std::mem::align_of::<Position>());
        assert_eq!(pos_info.id(), std::any::TypeId::of::<Position>());
    }

    #[test]
    fn test_bundle_def() {
        assert_eq!(TestBundle::COUNT, 3);

        let layouts = TestBundle::get_layouts();
        assert_eq!(layouts.len(), 3);

        // Check that layouts match expected component sizes
        assert_eq!(layouts[0], (std::mem::size_of::<Position>(), std::mem::align_of::<Position>()));
        assert_eq!(layouts[1], (std::mem::size_of::<Velocity>(), std::mem::align_of::<Velocity>()));
        assert_eq!(layouts[2], (std::mem::size_of::<Health>(), std::mem::align_of::<Health>()));
    }

    #[test]
    fn test_structure_of_arrays_creation() {
        let allocator = HeapAllocator::new();
        let soa = StructureOfArrays::<TestBundle, _>::new(allocator, 10);

        assert_eq!(soa.len(), 0);
        assert_eq!(soa.capacity(), 10);
        assert!(soa.is_empty());
    }

    #[test]
    fn test_structure_of_arrays_reserve() {
        let allocator = HeapAllocator::new();
        let mut soa = StructureOfArrays::<TestBundle, _>::new(allocator, 5);

        assert_eq!(soa.capacity(), 5);

        soa.reserve(20);
        assert_eq!(soa.capacity(), 20);

        // Reserving less than current capacity should not change it
        soa.reserve(10);
        assert_eq!(soa.capacity(), 20);
    }

    #[test]
    fn test_structure_of_arrays_clear() {
        let allocator = HeapAllocator::new();
        let mut soa = StructureOfArrays::<TestBundle, _>::new(allocator, 10);

        // Simulate adding some bundles (we'll implement push later)
        soa.num_of_bundles = 5;
        assert_eq!(soa.len(), 5);
        assert!(!soa.is_empty());

        soa.clear();
        assert_eq!(soa.len(), 0);
        assert!(soa.is_empty());
        assert_eq!(soa.capacity(), 10); // Capacity should remain unchanged
    }

    #[test]
    fn test_component_layout_access() {
        let allocator = HeapAllocator::new();
        let soa = StructureOfArrays::<TestBundle, _>::new(allocator, 10);

        // Test valid component indices
        assert!(soa.get_component_layout(0).is_some());
        assert!(soa.get_component_layout(1).is_some());
        assert!(soa.get_component_layout(2).is_some());

        // Test invalid component index
        assert!(soa.get_component_layout(3).is_none());
        assert!(soa.get_component_layout(100).is_none());
    }

    #[test]
    fn test_component_array_ptr_access() {
        let allocator = HeapAllocator::new();
        let soa = StructureOfArrays::<TestBundle, _>::new(allocator, 10);

        // Test valid component indices
        unsafe {
            assert!(soa.get_component_array_ptr(0).is_some());
            assert!(soa.get_component_array_ptr(1).is_some());
            assert!(soa.get_component_array_ptr(2).is_some());
        }

        // Test invalid component index
        unsafe {
            assert!(soa.get_component_array_ptr(3).is_none());
            assert!(soa.get_component_array_ptr(100).is_none());
        }
    }
}
