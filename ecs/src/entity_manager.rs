use crate::ecs::entity::{Entity, EntityType};
use std::collections::VecDeque;
use std::sync::{Arc, OnceLock};

/// - combine as the entity ID：entity_id = (generation << GENERATION_SHIFT) | index;
/// - get index：index = entity_id & INDEX_MASK;
/// - get generation：generation = entity_id >> GENERATION_SHIFT;
const GENERATION_SHIFT: u32 = 17; // use lower 17 bits for index
const RAW_INDEX_COUNT: u32 = 1 << GENERATION_SHIFT;
const INDEX_MASK: u32 = RAW_INDEX_COUNT - 1;

const MIN_FREE_INDICES: usize = 1024;

pub(crate) trait EntityListener: Send + Sync + 'static {
    fn on_entity_destroyed(&self, entity: &[Entity]);
}

pub struct EntityManager {
    gens: Box<[u8]>,
    current_index: u32,
    free_list_lock: std::sync::Mutex<()>,
    free_list: VecDeque<EntityType>,
    listener_lock: std::sync::Mutex<()>,
    listeners: Vec<Arc<dyn EntityListener>>,
}

impl Default for EntityManager {
    fn default() -> Self {
        Self::new()
    }
}

impl EntityManager {
    /// Singleton getter
    pub fn get_instance() -> &'static EntityManager {
        static INSTANCE: OnceLock<EntityManager> = OnceLock::new();
        INSTANCE.get_or_init(|| EntityManager::new())
    }

    /// maximum number of entities that can exist at the same time
    pub fn get_max_entity_count() -> usize {
        // 0 is reserved for null entities
        (RAW_INDEX_COUNT - 1) as usize
    }

    /// number of active entities
    #[inline(never)]
    pub fn get_entity_count(&self) -> usize {
        let _guard = self.free_list_lock.lock().unwrap();
        if self.current_index < RAW_INDEX_COUNT {
            (self.current_index - 1u32) as usize - self.free_list.len()
        } else {
            Self::get_max_entity_count() - self.free_list.len()
        }
    }

    fn get_generation(e: Entity) -> EntityType {
        e.id() >> GENERATION_SHIFT
    }

    fn get_index(e: Entity) -> EntityType {
        e.id() & INDEX_MASK
    }

    fn make_identity(generation: EntityType, index: EntityType) -> EntityType {
        (generation << GENERATION_SHIFT) | (index & INDEX_MASK)
    }

    fn get_listeners(&self) -> Vec<Arc<dyn EntityListener>> {
        let _guard = self.listener_lock.lock().unwrap(); // 确保 listeners 字段被此锁保护
        self.listeners.clone()
    }
}

impl EntityManager {
    /// Create a new EntityManager instance
    pub fn new() -> Self {
        Self {
            gens: vec![0u8; RAW_INDEX_COUNT as usize].into_boxed_slice(),
            current_index: 1, // Start from 1 since 0 is reserved for null entities
            free_list_lock: std::sync::Mutex::new(()),
            free_list: VecDeque::new(),
            listener_lock: std::sync::Mutex::new(()),
            listeners: Vec::new(),
        }
    }

    /// create n entities. Thread Safe
    pub fn create_n(&mut self, n: usize, entities: &mut [Entity]) {
        let gens = &self.gens;
        // available indexes
        let free_list = &mut self.free_list;

        let _guard = self.free_list_lock.lock().unwrap();

        let mut current_index_val = self.current_index;

        for entity_slot in entities.iter_mut().take(n) {
            // We have two options for index.
            // Either use the current index
            // or reuse the index while the free list is full or too long.
            let acquired_index_option: Option<EntityType> =
                if current_index_val < RAW_INDEX_COUNT && free_list.len() < MIN_FREE_INDICES {
                    let index_to_use = current_index_val;
                    current_index_val += 1;
                    Some(index_to_use)
                } else {
                    free_list.pop_front()
                };

            if let Some(index_to_use) = acquired_index_option {
                entity_slot.set_id(Self::make_identity(
                    gens[index_to_use as usize] as EntityType,
                    index_to_use,
                ));
            } else {
                *entity_slot = Entity::default()
            }
        }

        self.current_index = current_index_val;
    }

    /// destroy n entities. Thread Safe
    pub fn destroy_n(&mut self, n: usize, entities: &mut [Entity]) {
        // First, collect entities to destroy and validate them
        let mut entities_to_destroy = Vec::new();
        for entity in entities.iter_mut().take(n) {
            if entity.is_null() {
                continue;
            }

            if self.is_alive(*entity) {
                entities_to_destroy.push(*entity);
            }
        }

        // Now destroy the entities
        {
            let _free_list_lock = self.free_list_lock.lock().unwrap();
            for &entity in &entities_to_destroy {
                let index = Self::get_index(entity);
                self.free_list.push_back(index);
                self.gens[index as usize] += 1;
            }
        }

        // Clear the entities in the original array
        for entity in entities.iter_mut().take(n) {
            if entities_to_destroy.contains(entity) {
                entity.clear();
            }
        }

        // Notify listeners
        let listeners = self.get_listeners();
        for listener in listeners {
            listener.on_entity_destroyed(entities);
        }
    }

    /// create a new entity. Thread Safe
    /// Return Entity.is_null() if the entity cannot be allocated.
    pub fn create_one(&mut self) -> Entity {
        let mut entities = [Entity::default()];
        self.create_n(1, &mut entities);
        entities[0]
    }

    pub fn destroy_one(&mut self, entity: Entity) {
        let mut entities = [entity];
        self.destroy_n(1, &mut entities);
    }

    pub fn is_alive(&self, entity: Entity) -> bool {
        assert!(Self::get_index(entity) < RAW_INDEX_COUNT);
        entity.is_valid()
            && (Self::get_generation(entity)
                == self.gens[Self::get_index(entity) as usize] as EntityType)
    }

    pub fn register_listener(&mut self, listener: Arc<dyn EntityListener>) {
        let _guard = self.listener_lock.lock().unwrap();
        self.listeners.push(listener);
    }

    pub fn unregister_listener(&mut self, listener: Arc<dyn EntityListener>) {
        let _guard = self.listener_lock.lock().unwrap();
        self.listeners.retain(|l| !Arc::ptr_eq(l, &listener));
    }

    pub fn get_generation_for_index(&self, index: usize) -> u8 {
        self.gens[index]
    }
}
